# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=zero_gaspillage
DB_USER=root
DB_PASS=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=86400

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_PATH=uploads/

# Cron Job Configuration
CRON_ENABLED=true
