-- Zero Gaspillage Seed Data
USE zero_gaspillage;

-- Insert categories
INSERT INTO categories (name, slug, description, icon_url, sort_order) VALUES
('Fruits & Légumes', 'fruits-legumes', 'Fruits et légumes frais', '/icons/fruits.svg', 1),
('Produits Laitiers', 'produits-laitiers', 'Lait, fromage, yaourts', '/icons/dairy.svg', 2),
('Viandes & Poissons', 'viandes-poissons', 'Viandes fraîches et poissons', '/icons/meat.svg', 3),
('Boulangerie', 'boulangerie', 'Pain, viennoiseries, pâtisseries', '/icons/bakery.svg', 4),
('Épicerie', 'epicerie', 'Produits secs, conserves', '/icons/grocery.svg', 5),
('Surgelés', 'surgeles', 'Produits surgelés', '/icons/frozen.svg', 6),
('Boissons', 'boissons', 'Boissons diverses', '/icons/drinks.svg', 7);

-- Insert test users (passwords are hashed for 'password123')
INSERT INTO users (email, password_hash, first_name, last_name, phone, user_type, email_verified) VALUES
-- Admin user
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'System', '+33123456789', 'admin', TRUE),

-- Sellers
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jean', 'Dupont', '+***********', 'seller', TRUE),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Marie', 'Martin', '+***********', 'seller', TRUE),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Pierre', 'Durand', '+***********', 'seller', TRUE),

-- Clients
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sophie', 'Leroy', '+***********', 'client', TRUE),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Thomas', 'Bernard', '+***********', 'client', TRUE);

-- Insert seller profiles
INSERT INTO sellers_profiles (user_id, business_name, business_type, business_address, business_phone, description, is_verified, rating, total_reviews) VALUES
(2, 'Super Marché Central', 'supermarket', '123 Rue de la République, 75001 Paris', '+***********', 'Supermarché de quartier proposant des produits frais et de qualité', TRUE, 4.5, 150),
(3, 'Épicerie du Coin', 'grocery', '45 Avenue des Champs, 75008 Paris', '+***********', 'Épicerie familiale avec des produits locaux', TRUE, 4.2, 89),
(4, 'Ferme Bio Durand', 'producer', 'Chemin des Prés, 78000 Versailles', '+***********', 'Producteur bio de fruits et légumes de saison', TRUE, 4.8, 67);

-- Insert sample products with various expiry dates
INSERT INTO products (seller_id, category_id, name, description, original_price, current_price, discount_percentage, quantity_available, unit, expiry_date, production_date, brand, image_url, status) VALUES
-- Products expiring in 1-2 days (high discount)
(2, 1, 'Bananes Bio', 'Bananes biologiques mûres à point', 3.50, 1.40, 60.00, 15, 'kg', DATE_ADD(CURDATE(), INTERVAL 1 DAY), DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'Bio Nature', '/images/bananas.jpg', 'active'),
(2, 2, 'Yaourts Nature', 'Yaourts nature 4x125g', 4.20, 1.68, 60.00, 8, 'pack', DATE_ADD(CURDATE(), INTERVAL 2 DAY), DATE_SUB(CURDATE(), INTERVAL 12 DAY), 'Danone', '/images/yogurt.jpg', 'active'),

-- Products expiring in 3-5 days (medium discount)
(3, 4, 'Pain Complet', 'Pain complet artisanal', 2.80, 1.68, 40.00, 12, 'piece', DATE_ADD(CURDATE(), INTERVAL 4 DAY), CURDATE(), 'Boulangerie Artisan', '/images/bread.jpg', 'active'),
(3, 1, 'Tomates Cerises', 'Tomates cerises en barquette 250g', 3.90, 2.34, 40.00, 20, 'barquette', DATE_ADD(CURDATE(), INTERVAL 5 DAY), DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'Local', '/images/tomatoes.jpg', 'active'),

-- Products expiring in 6-14 days (low discount)
(4, 1, 'Pommes Gala', 'Pommes Gala de notre verger', 4.50, 3.60, 20.00, 25, 'kg', DATE_ADD(CURDATE(), INTERVAL 10 DAY), DATE_SUB(CURDATE(), INTERVAL 3 DAY), 'Ferme Durand', '/images/apples.jpg', 'active'),
(4, 1, 'Carottes Bio', 'Carottes biologiques fraîches', 2.90, 2.32, 20.00, 18, 'kg', DATE_ADD(CURDATE(), INTERVAL 12 DAY), DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'Ferme Durand', '/images/carrots.jpg', 'active'),

-- Products expiring in 15+ days (minimal discount)
(2, 5, 'Pâtes Complètes', 'Pâtes complètes 500g', 2.40, 2.16, 10.00, 30, 'pack', DATE_ADD(CURDATE(), INTERVAL 20 DAY), DATE_SUB(CURDATE(), INTERVAL 10 DAY), 'Barilla', '/images/pasta.jpg', 'active'),
(2, 7, 'Jus d\'Orange', 'Jus d\'orange 100% pur jus 1L', 3.20, 2.88, 10.00, 15, 'bottle', DATE_ADD(CURDATE(), INTERVAL 25 DAY), DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'Tropicana', '/images/orange-juice.jpg', 'active');
