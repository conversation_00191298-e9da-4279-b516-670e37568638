<?php

namespace App\Controllers;

use App\Utils\Database;
use App\Utils\JWT;

class OrderController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function store(?array $input): void
    {
        $user = JWT::requireAuth();

        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input']);
            return;
        }

        // Validate required fields
        $required = ['items', 'delivery_address', 'payment_method'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode(['error' => "Field {$field} is required"]);
                return;
            }
        }

        if (empty($input['items']) || !is_array($input['items'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Items array is required']);
            return;
        }

        try {
            $this->db->beginTransaction();

            // Group items by seller
            $sellerGroups = [];
            foreach ($input['items'] as $item) {
                $product = $this->db->fetch(
                    'SELECT * FROM products WHERE id = ? AND status = "active" AND quantity_available >= ?',
                    [$item['product_id'], $item['quantity']]
                );

                if (!$product) {
                    throw new \Exception("Product {$item['product_id']} not available");
                }

                $sellerId = $product['seller_id'];
                if (!isset($sellerGroups[$sellerId])) {
                    $sellerGroups[$sellerId] = [];
                }

                $sellerGroups[$sellerId][] = [
                    'product' => $product,
                    'quantity' => $item['quantity']
                ];
            }

            $orderIds = [];

            // Create separate orders for each seller
            foreach ($sellerGroups as $sellerId => $items) {
                $orderNumber = 'ZG' . date('Ymd') . rand(1000, 9999);
                $subtotal = 0;

                // Calculate subtotal
                foreach ($items as $item) {
                    $subtotal += $item['product']['current_price'] * $item['quantity'];
                }

                $taxAmount = $subtotal * 0.20; // 20% VAT
                $totalAmount = $subtotal + $taxAmount;

                // Create order
                $orderId = $this->db->insert('orders', [
                    'client_id' => $user['id'],
                    'seller_id' => $sellerId,
                    'order_number' => $orderNumber,
                    'status' => 'pending',
                    'payment_status' => 'pending',
                    'payment_method' => $input['payment_method'],
                    'subtotal' => $subtotal,
                    'tax_amount' => $taxAmount,
                    'total_amount' => $totalAmount,
                    'delivery_address' => $input['delivery_address'],
                    'delivery_phone' => $input['delivery_phone'] ?? null,
                    'delivery_notes' => $input['delivery_notes'] ?? null
                ]);

                // Create order items and update product quantities
                foreach ($items as $item) {
                    $this->db->insert('order_items', [
                        'order_id' => $orderId,
                        'product_id' => $item['product']['id'],
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['product']['current_price'],
                        'total_price' => $item['product']['current_price'] * $item['quantity'],
                        'product_name' => $item['product']['name'],
                        'product_expiry_date' => $item['product']['expiry_date']
                    ]);

                    // Update product quantity
                    $newQuantity = $item['product']['quantity_available'] - $item['quantity'];
                    $status = $newQuantity <= 0 ? 'sold_out' : 'active';
                    
                    $this->db->update('products', [
                        'quantity_available' => $newQuantity,
                        'status' => $status
                    ], 'id = ?', [$item['product']['id']]);
                }

                $orderIds[] = $orderId;
            }

            $this->db->commit();

            http_response_code(201);
            echo json_encode([
                'message' => 'Orders created successfully',
                'order_ids' => $orderIds
            ]);

        } catch (\Exception $e) {
            $this->db->rollback();
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    public function show(string $id): void
    {
        $user = JWT::requireAuth();

        $sql = "
            SELECT 
                o.*,
                u_client.first_name as client_first_name,
                u_client.last_name as client_last_name,
                u_client.email as client_email,
                sp.business_name,
                sp.business_type
            FROM orders o
            LEFT JOIN users u_client ON o.client_id = u_client.id
            LEFT JOIN users u_seller ON o.seller_id = u_seller.id
            LEFT JOIN sellers_profiles sp ON u_seller.id = sp.user_id
            WHERE o.id = ? AND (o.client_id = ? OR o.seller_id = ?)
        ";

        $order = $this->db->fetch($sql, [$id, $user['id'], $user['id']]);

        if (!$order) {
            http_response_code(404);
            echo json_encode(['error' => 'Order not found']);
            return;
        }

        // Get order items
        $items = $this->db->fetchAll(
            'SELECT * FROM order_items WHERE order_id = ?',
            [$id]
        );

        $order['items'] = $items;

        echo json_encode(['order' => $order]);
    }

    public function index(): void
    {
        $user = JWT::requireAuth();
        $page = (int) ($_GET['page'] ?? 1);
        $limit = (int) ($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? null;

        $offset = ($page - 1) * $limit;

        $whereConditions = ['(o.client_id = ? OR o.seller_id = ?)'];
        $params = [$user['id'], $user['id']];

        if ($status) {
            $whereConditions[] = 'o.status = ?';
            $params[] = $status;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                o.*,
                u_client.first_name as client_first_name,
                u_client.last_name as client_last_name,
                sp.business_name,
                COUNT(oi.id) as items_count
            FROM orders o
            LEFT JOIN users u_client ON o.client_id = u_client.id
            LEFT JOIN users u_seller ON o.seller_id = u_seller.id
            LEFT JOIN sellers_profiles sp ON u_seller.id = sp.user_id
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE {$whereClause}
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $orders = $this->db->fetchAll($sql, $params);

        echo json_encode(['orders' => $orders]);
    }

    public function updateStatus(string $id, ?array $input): void
    {
        $user = JWT::requireAuth();

        if (!$input || empty($input['status'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Status is required']);
            return;
        }

        $allowedStatuses = ['confirmed', 'preparing', 'ready', 'delivered', 'cancelled'];
        if (!in_array($input['status'], $allowedStatuses)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid status']);
            return;
        }

        // Verify order belongs to seller (only sellers can update status)
        $order = $this->db->fetch('SELECT * FROM orders WHERE id = ? AND seller_id = ?', [$id, $user['id']]);
        if (!$order) {
            http_response_code(404);
            echo json_encode(['error' => 'Order not found']);
            return;
        }

        try {
            $this->db->update('orders', ['status' => $input['status']], 'id = ?', [$id]);
            
            // Create notification for client
            $this->db->insert('notifications', [
                'user_id' => $order['client_id'],
                'type' => 'order_status',
                'title' => 'Order Status Updated',
                'message' => "Your order #{$order['order_number']} status has been updated to {$input['status']}",
                'data' => json_encode(['order_id' => $id, 'status' => $input['status']])
            ]);

            echo json_encode(['message' => 'Order status updated successfully']);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update order status']);
        }
    }
}
