<?php

namespace App\Utils;

use Firebase\JWT\JWT as FirebaseJWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;

class JWT
{
    private static string $secret;
    private static int $expiry;

    public static function init(): void
    {
        self::$secret = $_ENV['JWT_SECRET'] ?? 'default-secret-change-in-production';
        self::$expiry = (int) ($_ENV['JWT_EXPIRY'] ?? 86400); // 24 hours default
    }

    public static function encode(array $payload): string
    {
        if (!isset(self::$secret)) {
            self::init();
        }

        $payload['iat'] = time();
        $payload['exp'] = time() + self::$expiry;

        return FirebaseJWT::encode($payload, self::$secret, 'HS256');
    }

    public static function decode(string $token): ?array
    {
        if (!isset(self::$secret)) {
            self::init();
        }

        try {
            $decoded = FirebaseJWT::decode($token, new Key(self::$secret, 'HS256'));
            return (array) $decoded;
        } catch (ExpiredException $e) {
            return null;
        } catch (SignatureInvalidException $e) {
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getTokenFromHeader(): ?string
    {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;

        if (!$authHeader) {
            return null;
        }

        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        return null;
    }

    public static function getCurrentUser(): ?array
    {
        $token = self::getTokenFromHeader();
        if (!$token) {
            return null;
        }

        $payload = self::decode($token);
        if (!$payload) {
            return null;
        }

        // Get user from database
        $db = Database::getInstance();
        $user = $db->fetch(
            'SELECT id, email, first_name, last_name, user_type, is_active FROM users WHERE id = ? AND is_active = 1',
            [$payload['user_id']]
        );

        return $user;
    }

    public static function requireAuth(): array
    {
        $user = self::getCurrentUser();
        if (!$user) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit();
        }
        return $user;
    }

    public static function requireRole(string $role): array
    {
        $user = self::requireAuth();
        if ($user['user_type'] !== $role) {
            http_response_code(403);
            echo json_encode(['error' => 'Forbidden']);
            exit();
        }
        return $user;
    }
}
