<?php

namespace App\Controllers;

use App\Utils\Database;
use App\Utils\JWT;

class AuthController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function register(?array $input): void
    {
        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input']);
            return;
        }

        // Validate required fields
        $required = ['email', 'password', 'first_name', 'last_name', 'user_type'];
        foreach ($required as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode(['error' => "Field {$field} is required"]);
                return;
            }
        }

        // Validate email format
        if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid email format']);
            return;
        }

        // Validate user type
        if (!in_array($input['user_type'], ['client', 'seller'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid user type']);
            return;
        }

        // Check if email already exists
        $existingUser = $this->db->fetch('SELECT id FROM users WHERE email = ?', [$input['email']]);
        if ($existingUser) {
            http_response_code(409);
            echo json_encode(['error' => 'Email already registered']);
            return;
        }

        // Hash password
        $passwordHash = password_hash($input['password'], PASSWORD_DEFAULT);

        try {
            $this->db->beginTransaction();

            // Create user
            $userId = $this->db->insert('users', [
                'email' => $input['email'],
                'password_hash' => $passwordHash,
                'first_name' => $input['first_name'],
                'last_name' => $input['last_name'],
                'phone' => $input['phone'] ?? null,
                'user_type' => $input['user_type'],
                'email_verified' => false,
                'is_active' => true
            ]);

            // If seller, create seller profile
            if ($input['user_type'] === 'seller') {
                $this->db->insert('sellers_profiles', [
                    'user_id' => $userId,
                    'business_name' => $input['business_name'] ?? '',
                    'business_type' => $input['business_type'] ?? 'grocery',
                    'business_address' => $input['business_address'] ?? '',
                    'business_phone' => $input['business_phone'] ?? $input['phone'] ?? '',
                    'description' => $input['description'] ?? '',
                    'is_verified' => false
                ]);
            }

            $this->db->commit();

            // Generate JWT token
            $token = JWT::encode(['user_id' => $userId]);

            // Get user data
            $user = $this->db->fetch(
                'SELECT id, email, first_name, last_name, user_type, email_verified FROM users WHERE id = ?',
                [$userId]
            );

            http_response_code(201);
            echo json_encode([
                'message' => 'User registered successfully',
                'token' => $token,
                'user' => $user
            ]);

        } catch (\Exception $e) {
            $this->db->rollback();
            http_response_code(500);
            echo json_encode(['error' => 'Registration failed']);
        }
    }

    public function login(?array $input): void
    {
        if (!$input || empty($input['email']) || empty($input['password'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Email and password are required']);
            return;
        }

        // Get user from database
        $user = $this->db->fetch(
            'SELECT id, email, password_hash, first_name, last_name, user_type, is_active FROM users WHERE email = ?',
            [$input['email']]
        );

        if (!$user || !$user['is_active']) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid credentials']);
            return;
        }

        // Verify password
        if (!password_verify($input['password'], $user['password_hash'])) {
            http_response_code(401);
            echo json_encode(['error' => 'Invalid credentials']);
            return;
        }

        // Generate JWT token
        $token = JWT::encode(['user_id' => $user['id']]);

        // Remove password hash from response
        unset($user['password_hash']);

        echo json_encode([
            'message' => 'Login successful',
            'token' => $token,
            'user' => $user
        ]);
    }

    public function logout(): void
    {
        // For JWT, logout is handled client-side by removing the token
        echo json_encode(['message' => 'Logged out successfully']);
    }

    public function me(): void
    {
        $user = JWT::requireAuth();
        
        // Get additional user data if seller
        if ($user['user_type'] === 'seller') {
            $sellerProfile = $this->db->fetch(
                'SELECT business_name, business_type, is_verified, rating FROM sellers_profiles WHERE user_id = ?',
                [$user['id']]
            );
            $user['seller_profile'] = $sellerProfile;
        }

        echo json_encode(['user' => $user]);
    }
}
