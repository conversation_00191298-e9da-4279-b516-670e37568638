<?php
/**
 * Zero Gaspillage API Entry Point
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:5173');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Access-Control-Allow-Credentials: true');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;
use App\Utils\Router;
use App\Utils\Database;
use App\Controllers\AuthController;
use App\Controllers\ProductController;
use App\Controllers\OrderController;
use App\Controllers\SellerController;
use App\Controllers\AdminController;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Initialize database connection
try {
    Database::getInstance();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Initialize router
$router = new Router();

// Auth routes
$router->post('/api/auth/register', [AuthController::class, 'register']);
$router->post('/api/auth/login', [AuthController::class, 'login']);
$router->post('/api/auth/logout', [AuthController::class, 'logout']);
$router->get('/api/auth/me', [AuthController::class, 'me']);

// Product routes
$router->get('/api/products', [ProductController::class, 'index']);
$router->get('/api/products/{id}', [ProductController::class, 'show']);
$router->post('/api/products', [ProductController::class, 'store']);
$router->put('/api/products/{id}', [ProductController::class, 'update']);
$router->delete('/api/products/{id}', [ProductController::class, 'destroy']);

// Order routes
$router->post('/api/orders', [OrderController::class, 'store']);
$router->get('/api/orders/{id}', [OrderController::class, 'show']);
$router->get('/api/orders', [OrderController::class, 'index']);
$router->put('/api/orders/{id}/status', [OrderController::class, 'updateStatus']);

// Seller routes
$router->get('/api/seller/products', [SellerController::class, 'products']);
$router->get('/api/seller/orders', [SellerController::class, 'orders']);
$router->get('/api/seller/dashboard', [SellerController::class, 'dashboard']);

// Admin routes
$router->get('/api/admin/users', [AdminController::class, 'users']);
$router->get('/api/admin/reports', [AdminController::class, 'reports']);
$router->get('/api/admin/dashboard', [AdminController::class, 'dashboard']);

// Categories route
$router->get('/api/categories', [ProductController::class, 'categories']);

// Handle the request
try {
    $router->dispatch();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'Something went wrong'
    ]);
}
