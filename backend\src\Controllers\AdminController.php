<?php

namespace App\Controllers;

use App\Utils\Database;
use App\Utils\JWT;

class AdminController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function users(): void
    {
        JWT::requireRole('admin');
        
        $page = (int) ($_GET['page'] ?? 1);
        $limit = (int) ($_GET['limit'] ?? 20);
        $userType = $_GET['user_type'] ?? null;
        $search = $_GET['search'] ?? null;

        $offset = ($page - 1) * $limit;

        $whereConditions = [];
        $params = [];

        if ($userType) {
            $whereConditions[] = 'u.user_type = ?';
            $params[] = $userType;
        }

        if ($search) {
            $whereConditions[] = '(u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)';
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }

        $whereClause = empty($whereConditions) ? '1=1' : implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                u.*,
                sp.business_name,
                sp.business_type,
                sp.is_verified as seller_verified,
                sp.rating as seller_rating,
                COUNT(DISTINCT o_client.id) as orders_as_client,
                COUNT(DISTINCT o_seller.id) as orders_as_seller
            FROM users u
            LEFT JOIN sellers_profiles sp ON u.id = sp.user_id
            LEFT JOIN orders o_client ON u.id = o_client.client_id
            LEFT JOIN orders o_seller ON u.id = o_seller.seller_id
            WHERE {$whereClause}
            GROUP BY u.id
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $users = $this->db->fetchAll($sql, $params);

        // Get total count
        $countSql = "SELECT COUNT(*) as total FROM users u WHERE {$whereClause}";
        $countParams = array_slice($params, 0, -2);
        $totalResult = $this->db->fetch($countSql, $countParams);
        $total = $totalResult['total'];

        echo json_encode([
            'users' => $users,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    }

    public function reports(): void
    {
        JWT::requireRole('admin');

        $period = $_GET['period'] ?? '30'; // days
        $type = $_GET['type'] ?? 'overview';

        switch ($type) {
            case 'overview':
                echo json_encode($this->getOverviewReport($period));
                break;
            case 'sales':
                echo json_encode($this->getSalesReport($period));
                break;
            case 'products':
                echo json_encode($this->getProductsReport($period));
                break;
            case 'users':
                echo json_encode($this->getUsersReport($period));
                break;
            default:
                http_response_code(400);
                echo json_encode(['error' => 'Invalid report type']);
        }
    }

    public function dashboard(): void
    {
        JWT::requireRole('admin');

        $stats = [];

        // Total users
        $stats['total_users'] = $this->db->fetch('SELECT COUNT(*) as count FROM users')['count'];
        $stats['total_clients'] = $this->db->fetch('SELECT COUNT(*) as count FROM users WHERE user_type = "client"')['count'];
        $stats['total_sellers'] = $this->db->fetch('SELECT COUNT(*) as count FROM users WHERE user_type = "seller"')['count'];

        // Total products
        $stats['total_products'] = $this->db->fetch('SELECT COUNT(*) as count FROM products WHERE status != "removed"')['count'];
        $stats['active_products'] = $this->db->fetch('SELECT COUNT(*) as count FROM products WHERE status = "active"')['count'];
        $stats['urgent_products'] = $this->db->fetch('SELECT COUNT(*) as count FROM products WHERE is_urgent = 1 AND status = "active"')['count'];

        // Total orders
        $stats['total_orders'] = $this->db->fetch('SELECT COUNT(*) as count FROM orders')['count'];
        $stats['pending_orders'] = $this->db->fetch('SELECT COUNT(*) as count FROM orders WHERE status = "pending"')['count'];
        $stats['completed_orders'] = $this->db->fetch('SELECT COUNT(*) as count FROM orders WHERE status = "delivered"')['count'];

        // Revenue
        $revenueResult = $this->db->fetch('SELECT COALESCE(SUM(total_amount), 0) as revenue FROM orders WHERE status = "delivered"');
        $stats['total_revenue'] = $revenueResult['revenue'];

        $monthlyRevenueResult = $this->db->fetch('SELECT COALESCE(SUM(total_amount), 0) as revenue FROM orders WHERE status = "delivered" AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())');
        $stats['monthly_revenue'] = $monthlyRevenueResult['revenue'];

        // Recent activity
        $recentOrders = $this->db->fetchAll(
            'SELECT o.*, u_client.first_name as client_name, sp.business_name FROM orders o LEFT JOIN users u_client ON o.client_id = u_client.id LEFT JOIN users u_seller ON o.seller_id = u_seller.id LEFT JOIN sellers_profiles sp ON u_seller.id = sp.user_id ORDER BY o.created_at DESC LIMIT 10'
        );

        $recentUsers = $this->db->fetchAll(
            'SELECT u.*, sp.business_name FROM users u LEFT JOIN sellers_profiles sp ON u.id = sp.user_id ORDER BY u.created_at DESC LIMIT 10'
        );

        echo json_encode([
            'stats' => $stats,
            'recent_orders' => $recentOrders,
            'recent_users' => $recentUsers
        ]);
    }

    private function getOverviewReport(string $period): array
    {
        $days = (int) $period;
        $dateCondition = "DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)";

        return [
            'period' => $days,
            'new_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE {$dateCondition}")['count'],
            'new_orders' => $this->db->fetch("SELECT COUNT(*) as count FROM orders WHERE {$dateCondition}")['count'],
            'new_products' => $this->db->fetch("SELECT COUNT(*) as count FROM products WHERE {$dateCondition}")['count'],
            'revenue' => $this->db->fetch("SELECT COALESCE(SUM(total_amount), 0) as revenue FROM orders WHERE status = 'delivered' AND {$dateCondition}")['revenue']
        ];
    }

    private function getSalesReport(string $period): array
    {
        $days = (int) $period;
        $dateCondition = "DATE(o.created_at) >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)";

        $salesByDay = $this->db->fetchAll(
            "SELECT DATE(o.created_at) as date, COUNT(*) as orders, SUM(o.total_amount) as revenue FROM orders o WHERE {$dateCondition} GROUP BY DATE(o.created_at) ORDER BY date"
        );

        $salesByCategory = $this->db->fetchAll(
            "SELECT c.name as category, COUNT(oi.id) as sales, SUM(oi.total_price) as revenue FROM order_items oi JOIN products p ON oi.product_id = p.id JOIN categories c ON p.category_id = c.id JOIN orders o ON oi.order_id = o.id WHERE {$dateCondition} GROUP BY c.id ORDER BY revenue DESC"
        );

        $topSellers = $this->db->fetchAll(
            "SELECT sp.business_name, COUNT(o.id) as orders, SUM(o.total_amount) as revenue FROM orders o JOIN users u ON o.seller_id = u.id JOIN sellers_profiles sp ON u.id = sp.user_id WHERE {$dateCondition} GROUP BY o.seller_id ORDER BY revenue DESC LIMIT 10"
        );

        return [
            'period' => $days,
            'sales_by_day' => $salesByDay,
            'sales_by_category' => $salesByCategory,
            'top_sellers' => $topSellers
        ];
    }

    private function getProductsReport(string $period): array
    {
        $days = (int) $period;

        $expiringProducts = $this->db->fetchAll(
            "SELECT p.*, sp.business_name, DATEDIFF(p.expiry_date, CURDATE()) as days_to_expiry FROM products p JOIN users u ON p.seller_id = u.id JOIN sellers_profiles sp ON u.id = sp.user_id WHERE p.status = 'active' AND p.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) ORDER BY p.expiry_date"
        );

        $topProducts = $this->db->fetchAll(
            "SELECT p.name, sp.business_name, COUNT(oi.id) as sales, SUM(oi.total_price) as revenue FROM order_items oi JOIN products p ON oi.product_id = p.id JOIN users u ON p.seller_id = u.id JOIN sellers_profiles sp ON u.id = sp.user_id JOIN orders o ON oi.order_id = o.id WHERE DATE(o.created_at) >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY) GROUP BY p.id ORDER BY sales DESC LIMIT 20"
        );

        return [
            'period' => $days,
            'expiring_products' => $expiringProducts,
            'top_products' => $topProducts
        ];
    }

    private function getUsersReport(string $period): array
    {
        $days = (int) $period;
        $dateCondition = "DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY)";

        $userGrowth = $this->db->fetchAll(
            "SELECT DATE(created_at) as date, user_type, COUNT(*) as count FROM users WHERE {$dateCondition} GROUP BY DATE(created_at), user_type ORDER BY date"
        );

        $activeUsers = $this->db->fetchAll(
            "SELECT u.*, sp.business_name, COUNT(DISTINCT o.id) as order_count FROM users u LEFT JOIN sellers_profiles sp ON u.id = sp.user_id LEFT JOIN orders o ON (u.id = o.client_id OR u.id = o.seller_id) WHERE DATE(o.created_at) >= DATE_SUB(CURDATE(), INTERVAL {$days} DAY) GROUP BY u.id ORDER BY order_count DESC LIMIT 20"
        );

        return [
            'period' => $days,
            'user_growth' => $userGrowth,
            'active_users' => $activeUsers
        ];
    }
}
