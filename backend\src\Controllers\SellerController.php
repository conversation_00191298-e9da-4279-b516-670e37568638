<?php

namespace App\Controllers;

use App\Utils\Database;
use App\Utils\JWT;

class SellerController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function products(): void
    {
        $user = JWT::requireRole('seller');
        $page = (int) ($_GET['page'] ?? 1);
        $limit = (int) ($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? null;

        $offset = ($page - 1) * $limit;

        $whereConditions = ['p.seller_id = ?'];
        $params = [$user['id']];

        if ($status) {
            $whereConditions[] = 'p.status = ?';
            $params[] = $status;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                p.*,
                c.name as category_name,
                DATEDIFF(p.expiry_date, CURDATE()) as days_to_expiry,
                COALESCE(SUM(oi.quantity), 0) as total_sold
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN order_items oi ON p.id = oi.product_id
            WHERE {$whereClause}
            GROUP BY p.id
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $products = $this->db->fetchAll($sql, $params);

        echo json_encode(['products' => $products]);
    }

    public function orders(): void
    {
        $user = JWT::requireRole('seller');
        $page = (int) ($_GET['page'] ?? 1);
        $limit = (int) ($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? null;

        $offset = ($page - 1) * $limit;

        $whereConditions = ['o.seller_id = ?'];
        $params = [$user['id']];

        if ($status) {
            $whereConditions[] = 'o.status = ?';
            $params[] = $status;
        }

        $whereClause = implode(' AND ', $whereConditions);

        $sql = "
            SELECT 
                o.*,
                u.first_name as client_first_name,
                u.last_name as client_last_name,
                u.email as client_email,
                u.phone as client_phone,
                COUNT(oi.id) as items_count
            FROM orders o
            LEFT JOIN users u ON o.client_id = u.id
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE {$whereClause}
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $orders = $this->db->fetchAll($sql, $params);

        echo json_encode(['orders' => $orders]);
    }

    public function dashboard(): void
    {
        $user = JWT::requireRole('seller');

        // Get dashboard statistics
        $stats = [];

        // Total products
        $stats['total_products'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM products WHERE seller_id = ? AND status != "removed"',
            [$user['id']]
        )['count'];

        // Active products
        $stats['active_products'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM products WHERE seller_id = ? AND status = "active"',
            [$user['id']]
        )['count'];

        // Urgent products (expiring in 2 days)
        $stats['urgent_products'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM products WHERE seller_id = ? AND is_urgent = 1 AND status = "active"',
            [$user['id']]
        )['count'];

        // Total orders
        $stats['total_orders'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM orders WHERE seller_id = ?',
            [$user['id']]
        )['count'];

        // Pending orders
        $stats['pending_orders'] = $this->db->fetch(
            'SELECT COUNT(*) as count FROM orders WHERE seller_id = ? AND status = "pending"',
            [$user['id']]
        )['count'];

        // Total revenue (completed orders)
        $revenueResult = $this->db->fetch(
            'SELECT COALESCE(SUM(total_amount), 0) as revenue FROM orders WHERE seller_id = ? AND status = "delivered"',
            [$user['id']]
        );
        $stats['total_revenue'] = $revenueResult['revenue'];

        // This month revenue
        $monthlyRevenueResult = $this->db->fetch(
            'SELECT COALESCE(SUM(total_amount), 0) as revenue FROM orders WHERE seller_id = ? AND status = "delivered" AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())',
            [$user['id']]
        );
        $stats['monthly_revenue'] = $monthlyRevenueResult['revenue'];

        // Recent orders
        $recentOrders = $this->db->fetchAll(
            'SELECT o.*, u.first_name, u.last_name FROM orders o LEFT JOIN users u ON o.client_id = u.id WHERE o.seller_id = ? ORDER BY o.created_at DESC LIMIT 5',
            [$user['id']]
        );

        // Products expiring soon
        $expiringProducts = $this->db->fetchAll(
            'SELECT * FROM products WHERE seller_id = ? AND status = "active" AND expiry_date <= DATE_ADD(CURDATE(), INTERVAL 3 DAY) ORDER BY expiry_date ASC LIMIT 10',
            [$user['id']]
        );

        // Sales by category (last 30 days)
        $salesByCategory = $this->db->fetchAll(
            'SELECT c.name as category, COUNT(oi.id) as sales_count, SUM(oi.total_price) as sales_amount FROM order_items oi JOIN products p ON oi.product_id = p.id JOIN categories c ON p.category_id = c.id JOIN orders o ON oi.order_id = o.id WHERE p.seller_id = ? AND o.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) GROUP BY c.id ORDER BY sales_amount DESC',
            [$user['id']]
        );

        echo json_encode([
            'stats' => $stats,
            'recent_orders' => $recentOrders,
            'expiring_products' => $expiringProducts,
            'sales_by_category' => $salesByCategory
        ]);
    }
}
