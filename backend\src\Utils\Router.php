<?php

namespace App\Utils;

class Router
{
    private array $routes = [];

    public function get(string $path, array $handler): void
    {
        $this->addRoute('GET', $path, $handler);
    }

    public function post(string $path, array $handler): void
    {
        $this->addRoute('POST', $path, $handler);
    }

    public function put(string $path, array $handler): void
    {
        $this->addRoute('PUT', $path, $handler);
    }

    public function delete(string $path, array $handler): void
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    private function addRoute(string $method, string $path, array $handler): void
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    public function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            $pattern = $this->convertPathToRegex($route['path']);
            if (preg_match($pattern, $uri, $matches)) {
                array_shift($matches); // Remove full match
                $this->callHandler($route['handler'], $matches);
                return;
            }
        }

        // No route found
        http_response_code(404);
        echo json_encode(['error' => 'Route not found']);
    }

    private function convertPathToRegex(string $path): string
    {
        // Convert {id} to named capture groups
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);
        return '#^' . $pattern . '$#';
    }

    private function callHandler(array $handler, array $params): void
    {
        [$class, $method] = $handler;
        
        if (!class_exists($class)) {
            throw new \Exception("Controller class {$class} not found");
        }

        $controller = new $class();
        
        if (!method_exists($controller, $method)) {
            throw new \Exception("Method {$method} not found in {$class}");
        }

        // Get request body for POST/PUT requests
        $input = null;
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT'])) {
            $input = json_decode(file_get_contents('php://input'), true);
        }

        // Call the controller method with parameters and input
        call_user_func_array([$controller, $method], array_merge($params, [$input]));
    }
}
