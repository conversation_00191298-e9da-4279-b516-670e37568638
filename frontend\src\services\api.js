import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/api/auth/register', userData),
  login: (credentials) => api.post('/api/auth/login', credentials),
  logout: () => api.post('/api/auth/logout'),
  me: () => api.get('/api/auth/me'),
};

// Products API
export const productsAPI = {
  getAll: (params = {}) => api.get('/api/products', { params }),
  getById: (id) => api.get(`/api/products/${id}`),
  create: (productData) => api.post('/api/products', productData),
  update: (id, productData) => api.put(`/api/products/${id}`, productData),
  delete: (id) => api.delete(`/api/products/${id}`),
  getCategories: () => api.get('/api/categories'),
};

// Orders API
export const ordersAPI = {
  create: (orderData) => api.post('/api/orders', orderData),
  getById: (id) => api.get(`/api/orders/${id}`),
  getAll: (params = {}) => api.get('/api/orders', { params }),
  updateStatus: (id, status) => api.put(`/api/orders/${id}/status`, { status }),
};

// Seller API
export const sellerAPI = {
  getProducts: (params = {}) => api.get('/api/seller/products', { params }),
  getOrders: (params = {}) => api.get('/api/seller/orders', { params }),
  getDashboard: () => api.get('/api/seller/dashboard'),
};

// Admin API
export const adminAPI = {
  getUsers: (params = {}) => api.get('/api/admin/users', { params }),
  getReports: (params = {}) => api.get('/api/admin/reports', { params }),
  getDashboard: () => api.get('/api/admin/dashboard'),
};

export default api;
