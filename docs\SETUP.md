# Zero Gaspillage - Setup Guide

## Project Overview

Zero Gaspillage is a marketplace platform that connects sellers (supermarkets, grocery stores, producers) with clients to sell near-expiry food items at discounted prices.

### Tech Stack
- **Frontend**: React + Vite, Tailwind CSS, Axios
- **Backend**: PHP 8.0+, REST API, JWT Authentication
- **Database**: MySQL 8.0+
- **Payments**: Stripe integration

## Prerequisites

- Node.js 18+
- PHP 8.0+
- MySQL 8.0+
- Composer (recommended)

## Installation Steps

### 1. Clone and Setup Project Structure

```bash
git clone <repository-url>
cd 0ZeroGasspillage
```

### 2. Database Setup

1. Create MySQL database:
```sql
CREATE DATABASE zero_gaspillage CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import schema and seed data:
```bash
mysql -u root -p zero_gaspillage < database/schema.sql
mysql -u root -p zero_gaspillage < database/seed.sql
```

### 3. Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Install dependencies (if Composer is available):
```bash
composer install
```

3. Configure environment:
```bash
cp .env.example .env
```

4. Update `.env` file with your database credentials:
```env
DB_HOST=localhost
DB_PORT=3306
DB_NAME=zero_gaspillage
DB_USER=your_username
DB_PASS=your_password
JWT_SECRET=your-super-secret-jwt-key
```

5. Start PHP development server:
```bash
php -S localhost:8000 -t public
```

### 4. Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment:
```bash
cp .env.example .env
```

4. Start development server:
```bash
npm run dev
```

## Default Accounts

The seed data includes test accounts:

### Admin Account
- Email: `<EMAIL>`
- Password: `password123`

### Seller Accounts
- Email: `<EMAIL>` (Supermarché)
- Email: `<EMAIL>` (Épicerie)
- Email: `<EMAIL>` (Producteur)
- Password: `password123` (for all)

### Client Accounts
- Email: `<EMAIL>`
- Email: `<EMAIL>`
- Password: `password123` (for all)

## Features Implemented

### ✅ Core Infrastructure
- [x] Project structure (React frontend, PHP backend, MySQL database)
- [x] Database schema with all required tables
- [x] JWT authentication system
- [x] REST API endpoints
- [x] Responsive UI with Tailwind CSS

### ✅ User Management
- [x] User registration (client/seller)
- [x] Login/logout functionality
- [x] User profiles and seller profiles
- [x] Role-based access control

### ✅ Product Management
- [x] Product CRUD operations
- [x] Category management
- [x] Automatic discount calculation based on expiry date
- [x] Product filtering and search
- [x] Image upload support

### ✅ Business Logic
- [x] TTL-based discount rules:
  - 30-14 days: 10-25% discount
  - 14-5 days: 30-50% discount
  - 5-2 days: 50-80% discount
  - ≤2 days: Mark as urgent
- [x] Automatic discount updates (cron job)
- [x] Inventory management

### ✅ UI Components
- [x] Responsive sidebar navigation
- [x] Product cards with expiry badges
- [x] Filter panel
- [x] Modal and toast notifications
- [x] Authentication forms

### 🚧 In Progress / TODO
- [ ] Shopping cart functionality
- [ ] Order management system
- [ ] Payment integration (Stripe)
- [ ] Seller dashboard
- [ ] Admin dashboard
- [ ] Email notifications
- [ ] File upload handling
- [ ] Advanced search and filters

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Products
- `GET /api/products` - List products with filters
- `GET /api/products/{id}` - Get product details
- `POST /api/products` - Create product (seller only)
- `PUT /api/products/{id}` - Update product (seller only)
- `DELETE /api/products/{id}` - Delete product (seller only)
- `GET /api/categories` - List categories

### Orders
- `POST /api/orders` - Create order
- `GET /api/orders` - List user orders
- `GET /api/orders/{id}` - Get order details
- `PUT /api/orders/{id}/status` - Update order status (seller only)

### Seller
- `GET /api/seller/products` - Seller's products
- `GET /api/seller/orders` - Seller's orders
- `GET /api/seller/dashboard` - Seller dashboard stats

### Admin
- `GET /api/admin/users` - List all users
- `GET /api/admin/reports` - Generate reports
- `GET /api/admin/dashboard` - Admin dashboard stats

## Cron Jobs

Set up the following cron job to automatically update discounts:

```bash
# Run every 6 hours
0 */6 * * * /usr/bin/php /path/to/backend/cron/update_discounts.php
```

## Development URLs

- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- Database: localhost:3306

## Security Features

- JWT token authentication
- Password hashing with PHP's `password_hash()`
- SQL injection prevention with prepared statements
- Input validation and sanitization
- CORS configuration
- Rate limiting (recommended for production)

## Deployment Notes

### Production Environment
1. Use a proper web server (Nginx/Apache)
2. Enable HTTPS with SSL certificates
3. Use environment variables for sensitive data
4. Set up proper database backups
5. Configure proper error logging
6. Use Composer for dependency management
7. Optimize frontend build for production

### Environment Variables
Ensure all sensitive data is stored in environment variables:
- Database credentials
- JWT secret key
- Stripe API keys
- SMTP credentials

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in `.env`
   - Ensure MySQL service is running
   - Verify database exists

2. **CORS Errors**
   - Check `Access-Control-Allow-Origin` headers
   - Ensure frontend URL matches backend CORS settings

3. **JWT Token Issues**
   - Verify JWT secret is set in backend `.env`
   - Check token expiry settings
   - Clear browser localStorage if needed

4. **File Upload Issues**
   - Check PHP upload limits
   - Verify upload directory permissions
   - Ensure file size limits are appropriate

## Next Steps

1. Complete shopping cart implementation
2. Integrate Stripe payment processing
3. Build comprehensive seller dashboard
4. Implement admin panel features
5. Add email notification system
6. Create mobile-responsive design
7. Add comprehensive testing
8. Optimize for production deployment
