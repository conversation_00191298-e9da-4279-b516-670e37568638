import React, { useState } from 'react';
import { Menu, Search, ShoppingCart, Bell, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Topbar = ({ onMenuToggle }) => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Navigate to products page with search query
      window.location.href = `/products?search=${encodeURIComponent(searchQuery.trim())}`;
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={onMenuToggle}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <Menu size={24} className="text-gray-600" />
          </button>

          {/* Logo for mobile */}
          <Link to="/" className="lg:hidden flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-1 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">ZG</span>
            </div>
            <span className="text-lg font-bold text-gray-800">Zero Gaspillage</span>
          </Link>
        </div>

        {/* Center section - Search */}
        <div className="flex-1 max-w-2xl mx-4">
          <form onSubmit={handleSearch} className="relative">
            <div className="relative">
              <Search 
                size={20} 
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
              />
              <input
                type="text"
                placeholder="Rechercher des produits..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-1 focus:border-transparent outline-none transition-all duration-200"
              />
            </div>
          </form>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-2">
          {user ? (
            <>
              {/* Notifications */}
              <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <Bell size={20} className="text-gray-600" />
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-orange text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Cart (for clients) */}
              {user.user_type === 'client' && (
                <Link 
                  to="/cart"
                  className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <ShoppingCart size={20} className="text-gray-600" />
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-green-1 text-white text-xs rounded-full flex items-center justify-center">
                    2
                  </span>
                </Link>
              )}

              {/* User menu */}
              <div className="relative group">
                <button className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <div className="w-8 h-8 bg-green-1 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">
                      {user.first_name?.[0]}{user.last_name?.[0]}
                    </span>
                  </div>
                  <span className="hidden md:block font-medium text-gray-700">
                    {user.first_name}
                  </span>
                </button>

                {/* Dropdown menu */}
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-2">
                    <Link
                      to="/profile"
                      className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                    >
                      <User size={16} />
                      <span>Mon Profil</span>
                    </Link>
                    {user.user_type === 'seller' && (
                      <Link
                        to="/seller/dashboard"
                        className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <User size={16} />
                        <span>Tableau de Bord</span>
                      </Link>
                    )}
                    {user.user_type === 'admin' && (
                      <Link
                        to="/admin/dashboard"
                        className="flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                      >
                        <User size={16} />
                        <span>Administration</span>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center space-x-2">
              <Link
                to="/login"
                className="px-4 py-2 text-green-1 hover:text-green-2 font-medium transition-colors duration-200"
              >
                Connexion
              </Link>
              <Link
                to="/register"
                className="px-4 py-2 bg-green-1 text-white rounded-lg hover:bg-green-2 font-medium transition-colors duration-200"
              >
                Inscription
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Topbar;
