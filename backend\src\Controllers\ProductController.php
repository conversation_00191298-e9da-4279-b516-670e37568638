<?php

namespace App\Controllers;

use App\Utils\Database;
use App\Utils\JWT;

class ProductController
{
    private Database $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    public function index(): void
    {
        $page = (int) ($_GET['page'] ?? 1);
        $limit = (int) ($_GET['limit'] ?? 20);
        $category = $_GET['category'] ?? null;
        $search = $_GET['search'] ?? null;
        $sortBy = $_GET['sort_by'] ?? 'created_at';
        $sortOrder = $_GET['sort_order'] ?? 'DESC';
        $urgent = $_GET['urgent'] ?? null;

        $offset = ($page - 1) * $limit;

        // Build WHERE clause
        $whereConditions = ['p.status = "active"'];
        $params = [];

        if ($category) {
            $whereConditions[] = 'p.category_id = ?';
            $params[] = $category;
        }

        if ($search) {
            $whereConditions[] = '(p.name LIKE ? OR p.description LIKE ?)';
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }

        if ($urgent === 'true') {
            $whereConditions[] = 'p.is_urgent = 1';
        }

        $whereClause = implode(' AND ', $whereConditions);

        // Validate sort parameters
        $allowedSortFields = ['created_at', 'current_price', 'discount_percentage', 'expiry_date'];
        if (!in_array($sortBy, $allowedSortFields)) {
            $sortBy = 'created_at';
        }
        if (!in_array(strtoupper($sortOrder), ['ASC', 'DESC'])) {
            $sortOrder = 'DESC';
        }

        // Get products with seller info
        $sql = "
            SELECT 
                p.*,
                c.name as category_name,
                c.slug as category_slug,
                sp.business_name,
                sp.business_type,
                sp.rating as seller_rating,
                DATEDIFF(p.expiry_date, CURDATE()) as days_to_expiry
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN users u ON p.seller_id = u.id
            LEFT JOIN sellers_profiles sp ON u.id = sp.user_id
            WHERE {$whereClause}
            ORDER BY p.{$sortBy} {$sortOrder}
            LIMIT ? OFFSET ?
        ";

        $params[] = $limit;
        $params[] = $offset;

        $products = $this->db->fetchAll($sql, $params);

        // Get total count for pagination
        $countSql = "
            SELECT COUNT(*) as total
            FROM products p
            WHERE {$whereClause}
        ";
        $countParams = array_slice($params, 0, -2); // Remove limit and offset
        $totalResult = $this->db->fetch($countSql, $countParams);
        $total = $totalResult['total'];

        echo json_encode([
            'products' => $products,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit)
            ]
        ]);
    }

    public function show(string $id): void
    {
        $sql = "
            SELECT 
                p.*,
                c.name as category_name,
                c.slug as category_slug,
                sp.business_name,
                sp.business_type,
                sp.business_address,
                sp.rating as seller_rating,
                sp.total_reviews,
                u.first_name as seller_first_name,
                u.last_name as seller_last_name,
                DATEDIFF(p.expiry_date, CURDATE()) as days_to_expiry
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN users u ON p.seller_id = u.id
            LEFT JOIN sellers_profiles sp ON u.id = sp.user_id
            WHERE p.id = ? AND p.status = 'active'
        ";

        $product = $this->db->fetch($sql, [$id]);

        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }

        // Increment views count
        $this->db->update('products', ['views_count' => $product['views_count'] + 1], 'id = ?', [$id]);

        echo json_encode(['product' => $product]);
    }

    public function store(?array $input): void
    {
        $user = JWT::requireRole('seller');

        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input']);
            return;
        }

        // Validate required fields
        $required = ['name', 'category_id', 'original_price', 'quantity_available', 'expiry_date'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || $input[$field] === '') {
                http_response_code(400);
                echo json_encode(['error' => "Field {$field} is required"]);
                return;
            }
        }

        // Validate expiry date
        $expiryDate = \DateTime::createFromFormat('Y-m-d', $input['expiry_date']);
        if (!$expiryDate || $expiryDate < new \DateTime()) {
            http_response_code(400);
            echo json_encode(['error' => 'Expiry date must be in the future']);
            return;
        }

        // Calculate discount based on days to expiry
        $daysToExpiry = $expiryDate->diff(new \DateTime())->days;
        $discountPercentage = $this->calculateDiscount($daysToExpiry);
        $currentPrice = $input['original_price'] * (1 - $discountPercentage / 100);

        try {
            $productId = $this->db->insert('products', [
                'seller_id' => $user['id'],
                'category_id' => $input['category_id'],
                'name' => $input['name'],
                'description' => $input['description'] ?? '',
                'original_price' => $input['original_price'],
                'current_price' => $currentPrice,
                'discount_percentage' => $discountPercentage,
                'quantity_available' => $input['quantity_available'],
                'unit' => $input['unit'] ?? 'piece',
                'expiry_date' => $input['expiry_date'],
                'production_date' => $input['production_date'] ?? null,
                'brand' => $input['brand'] ?? '',
                'barcode' => $input['barcode'] ?? '',
                'image_url' => $input['image_url'] ?? '',
                'additional_images' => isset($input['additional_images']) ? json_encode($input['additional_images']) : null,
                'is_urgent' => $daysToExpiry <= 2,
                'status' => 'active'
            ]);

            // Record discount history if discount applied
            if ($discountPercentage > 0) {
                $this->db->insert('product_discounts_history', [
                    'product_id' => $productId,
                    'old_price' => $input['original_price'],
                    'new_price' => $currentPrice,
                    'discount_percentage' => $discountPercentage,
                    'days_to_expiry' => $daysToExpiry
                ]);
            }

            http_response_code(201);
            echo json_encode([
                'message' => 'Product created successfully',
                'product_id' => $productId
            ]);

        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to create product']);
        }
    }

    public function categories(): void
    {
        $categories = $this->db->fetchAll(
            'SELECT id, name, slug, description, icon_url FROM categories WHERE is_active = 1 ORDER BY sort_order, name'
        );

        echo json_encode(['categories' => $categories]);
    }

    private function calculateDiscount(int $daysToExpiry): float
    {
        if ($daysToExpiry <= 2) {
            return rand(50, 80); // 50-80% discount
        } elseif ($daysToExpiry <= 5) {
            return rand(30, 50); // 30-50% discount
        } elseif ($daysToExpiry <= 14) {
            return rand(10, 25); // 10-25% discount
        } else {
            return 0; // No discount
        }
    }

    public function update(string $id, ?array $input): void
    {
        $user = JWT::requireRole('seller');
        
        // Verify product belongs to seller
        $product = $this->db->fetch('SELECT * FROM products WHERE id = ? AND seller_id = ?', [$id, $user['id']]);
        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }

        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input']);
            return;
        }

        // Update allowed fields
        $allowedFields = ['name', 'description', 'original_price', 'quantity_available', 'unit', 'brand', 'image_url'];
        $updateData = [];
        
        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateData[$field] = $input[$field];
            }
        }

        if (empty($updateData)) {
            http_response_code(400);
            echo json_encode(['error' => 'No valid fields to update']);
            return;
        }

        try {
            $this->db->update('products', $updateData, 'id = ?', [$id]);
            echo json_encode(['message' => 'Product updated successfully']);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update product']);
        }
    }

    public function destroy(string $id): void
    {
        $user = JWT::requireRole('seller');
        
        // Verify product belongs to seller
        $product = $this->db->fetch('SELECT * FROM products WHERE id = ? AND seller_id = ?', [$id, $user['id']]);
        if (!$product) {
            http_response_code(404);
            echo json_encode(['error' => 'Product not found']);
            return;
        }

        try {
            $this->db->update('products', ['status' => 'removed'], 'id = ?', [$id]);
            echo json_encode(['message' => 'Product removed successfully']);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to remove product']);
        }
    }
}
