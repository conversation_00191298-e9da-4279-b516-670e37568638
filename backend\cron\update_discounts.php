<?php
/**
 * Cron job to update product discounts based on expiry dates
 * Run every 6 hours: 0 */6 * * * /usr/bin/php /path/to/backend/cron/update_discounts.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Dotenv\Dotenv;
use App\Utils\Database;

// Load environment variables
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Check if cron is enabled
if (($_ENV['CRON_ENABLED'] ?? 'false') !== 'true') {
    echo "Cron jobs are disabled\n";
    exit(0);
}

try {
    $db = Database::getInstance();
    
    echo "Starting discount update process...\n";
    
    // Get all active products
    $products = $db->fetchAll(
        'SELECT id, name, original_price, current_price, discount_percentage, expiry_date, seller_id 
         FROM products 
         WHERE status = "active" AND expiry_date >= CURDATE()'
    );
    
    $updatedCount = 0;
    $urgentCount = 0;
    $expiredCount = 0;
    
    foreach ($products as $product) {
        $expiryDate = new DateTime($product['expiry_date']);
        $today = new DateTime();
        $daysToExpiry = $today->diff($expiryDate)->days;
        
        // Skip if expiry date has passed
        if ($expiryDate < $today) {
            // Mark as expired
            $db->update('products', ['status' => 'expired'], 'id = ?', [$product['id']]);
            $expiredCount++;
            continue;
        }
        
        // Calculate new discount based on days to expiry
        $newDiscountPercentage = calculateDiscount($daysToExpiry);
        $newPrice = $product['original_price'] * (1 - $newDiscountPercentage / 100);
        $isUrgent = $daysToExpiry <= 2;
        
        // Only update if discount has changed significantly (more than 1%)
        if (abs($newDiscountPercentage - $product['discount_percentage']) > 1) {
            $db->beginTransaction();
            
            try {
                // Update product
                $db->update('products', [
                    'current_price' => $newPrice,
                    'discount_percentage' => $newDiscountPercentage,
                    'is_urgent' => $isUrgent
                ], 'id = ?', [$product['id']]);
                
                // Record discount history
                $db->insert('product_discounts_history', [
                    'product_id' => $product['id'],
                    'old_price' => $product['current_price'],
                    'new_price' => $newPrice,
                    'discount_percentage' => $newDiscountPercentage,
                    'days_to_expiry' => $daysToExpiry
                ]);
                
                // Create notification for seller
                $message = "Discount updated for '{$product['name']}' - Now {$newDiscountPercentage}% off";
                if ($isUrgent) {
                    $message .= " (URGENT - expires in {$daysToExpiry} days)";
                }
                
                $db->insert('notifications', [
                    'user_id' => $product['seller_id'],
                    'type' => $isUrgent ? 'product_urgent' : 'discount_applied',
                    'title' => $isUrgent ? 'Product Urgent' : 'Discount Applied',
                    'message' => $message,
                    'data' => json_encode([
                        'product_id' => $product['id'],
                        'old_discount' => $product['discount_percentage'],
                        'new_discount' => $newDiscountPercentage,
                        'days_to_expiry' => $daysToExpiry
                    ])
                ]);
                
                $db->commit();
                $updatedCount++;
                
                if ($isUrgent) {
                    $urgentCount++;
                }
                
                echo "Updated product ID {$product['id']}: {$product['discount_percentage']}% -> {$newDiscountPercentage}%\n";
                
            } catch (Exception $e) {
                $db->rollback();
                echo "Error updating product ID {$product['id']}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Clean up old notifications (older than 30 days)
    $db->query('DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)');
    
    // Clean up old discount history (older than 90 days)
    $db->query('DELETE FROM product_discounts_history WHERE applied_at < DATE_SUB(NOW(), INTERVAL 90 DAY)');
    
    echo "Discount update completed:\n";
    echo "- Products updated: {$updatedCount}\n";
    echo "- Products marked urgent: {$urgentCount}\n";
    echo "- Products expired: {$expiredCount}\n";
    echo "- Total products processed: " . count($products) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

function calculateDiscount(int $daysToExpiry): float
{
    if ($daysToExpiry <= 2) {
        return rand(50, 80); // 50-80% discount
    } elseif ($daysToExpiry <= 5) {
        return rand(30, 50); // 30-50% discount
    } elseif ($daysToExpiry <= 14) {
        return rand(10, 25); // 10-25% discount
    } else {
        return 0; // No discount
    }
}
